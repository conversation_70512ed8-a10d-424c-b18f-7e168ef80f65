<!-- Generated by SpecStory -->

# 技术问答知识库架构与选型讨论 (2025-08-18 02:39Z)

_**User**_

当前我的项目是自己搭建的rag知识库，我们现在想做一个更大的产品知识技术问答的知识库，要求后面     
  可以横推这个技术，用户只需要做文档上传同步就可以了，当前的问题主要还是准确度不够，80%左右，     
  而且做了很多本地化的特调。我希望有个架构可以实现当前代码实现且可以持续演进，帮我从市面上做      
  个深度调研，并指定一份技术选型文档，你看你还需要什么信息，不急着做，先讨论清楚

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要我帮他做一个RAG知识库的技术选型和架构调研。从他的描述来看：

1. 当前情况：
   - 已有一个自建的RAG知识库
   - 准确度约80%
   - 做了很多本地化特调
   - 工作在Windows环境，Java项目

2. 目标：
   - 构建更大的产品知识技术问答知识库
   - 可以横向推广，用户只需上传文档同步
   - 需要提升准确度
   - 需要可持续演进的架构

3. 需求：
   - 深度市场调研
   - 技术选型文档
   - 架构设计

我需要首先了解他当前的代码实现情况，然后才能做好调研和选型建议。让我先看看当前的项目结构和实现。</details></think>

---

您好！这是一个很好的技术升级项目。为了给您提供最准确和有针对性的技术调研和选型建议，我需要先了解您当前的实现情况。

让我先探索一下您当前的项目架构和实现：

---

<details>
            <summary>Listed current directory • **63** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `.claude` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |
| 📁 `.idea` |
| 📄 `InfoGo图谱数据导入API接口规范.md` |
| 📄 `知识图谱表结构文档.md` |
| 📁 `logs` |
| 📁 `rerank` |
| 📄 `test_monitor_fix.py` |
| 📁 `service` |
| 📁 `controller` |
| 📄 `set_utf8.ps1` |
| 📄 `监控系统使用说明.md` |
| 📄 `view_monitor.py` |
| 📁 `utils` |
| 📄 `test_monitor_simple.py` |
| 📄 `test_new_monitor.py` |
| 📄 `test_monitor_update.py` |
| 📁 `monitor_data` |
| 📁 `monitor` |
| 📄 `pkl_viewer.py` |
| 📄 `view_monitor_data.py` |
| 📄 `test_monitor_local.py` |
| 📄 `demo_monitor.py` |
| 📄 `main.py` |
| 📄 `test_monitor.py` |
| 📁 `retrieve` |
| 📄 `recall_analysis_report.md` |
| 📄 `analyze_recall_contribution.py` |
| 📁 `doc` |
| 📄 `plain-chinese.puml` |
| 📄 `install_plantuml.bat` |
| 📄 `test_chinese.puml` |
| 📄 `fix_plantuml_chinese.md` |
| 📄 `architecture_diagrams_README.md` |
| 📄 `kg_roi_analysis.md` |
| 📄 `FAQ接口全方位技术分析报告.md` |
| 📄 `complex_query_comparison.py` |
| 📄 `sql_vs_graph_comparison.md` |
| 📄 `debug_test.py` |
| 📄 `test_api.py` |
| 📁 `resource` |
| 📁 `__pycache__` |
| 📁 `rewrite` |
| 📁 `embedding` |
| 📁 `llm` |
| 📄 `proxy_config.py` |
| 📁 `entity` |
| 📁 `prompt` |
| 📁 `apollo` |
| 📄 `service_setting.cfg` |
| 📁 `test` |
| 📄 `test.py` |
| 📄 `test.txt` |
| 📁 `ui` |
| 📄 `.gitignore` |
| 📄 `config.py` |
| 📄 `Dockerfile` |
| 📁 `domain` |
| 📁 `infrastructure` |
| 📄 `requirements.txt` |
| 📄 `__init__.py` |

</details>

---

<think><details><summary>Thought Process</summary>我看到这是一个Python项目，而不是Java项目（用户之前说的可能有误）。项目结构看起来是一个相当完整的RAG系统，包含了：

1. 基础组件：embedding, llm, monitor, retrieve, rerank
2. 业务层：controller, service, domain
3. 基础设施：infrastructure
4. 工具和测试：utils, test</details></think>

---

